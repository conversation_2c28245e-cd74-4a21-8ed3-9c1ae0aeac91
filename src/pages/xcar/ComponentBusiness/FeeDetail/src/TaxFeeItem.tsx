import React, { useState } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { ITaxFeeItem, ITaxFeeSubItem } from './Types';
import { ItemPrice } from './Common';
import styles from './Styles';

const TaxFeeSubItem: React.FC<ITaxFeeSubItem & { theme?: any }> = ({
  title,
  description,
  currencyCode,
  currentTotalPrice,
  theme,
}) => {
  const { bbkFeeDetailGrayDetailColor } = theme || {};

  return (
    <View style={styles.taxFeeSubItemWrap}>
      <View style={styles.taxFeeSubItemLine}>
        <Text style={styles.taxFeeSubItemTitle}>{title}</Text>
        {currentTotalPrice !== undefined && (
          <ItemPrice
            currency={currencyCode}
            price={currentTotalPrice}
            style={styles.taxFeeSubItemPrice}
          />
        )}
      </View>
      {!!description && (
        <Text
          style={xMergeStyles([
            styles.taxFeeSubItemDesc,
            { color: bbkFeeDetailGrayDetailColor || color.fontSubDark },
          ])}
        >
          {description}
        </Text>
      )}
    </View>
  );
};

const TaxFeeItem: React.FC<ITaxFeeItem> = ({
  title,
  currencyCode,
  currentTotalPrice,
  items = [],
  theme,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const hasSubItems = items && items.length > 0;

  return (
    <View style={styles.taxFeeItemWrap}>
      <Touchable
        onPress={hasSubItems ? toggleExpanded : undefined}
        style={styles.taxFeeItemHeader}
      >
        <View style={styles.taxFeeItemHeaderContent}>
          <View style={styles.taxFeeItemTitleWrap}>
            <Text style={styles.taxFeeItemTitle}>{title}</Text>
            {hasSubItems && (
              <Text type="icon" style={styles.taxFeeItemExpandIcon}>
                {isExpanded ? icon.arrowUp : icon.arrowDown}
              </Text>
            )}
          </View>
          {currentTotalPrice !== undefined && (
            <ItemPrice
              currency={currencyCode}
              price={currentTotalPrice}
              style={styles.taxFeeItemPrice}
            />
          )}
        </View>
      </Touchable>

      {isExpanded && hasSubItems && (
        <View style={styles.taxFeeItemContent}>
          {items.map((item, index) => (
            <TaxFeeSubItem
              key={`${item.code}_${index}`}
              {...item}
              theme={theme}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default TaxFeeItem;
