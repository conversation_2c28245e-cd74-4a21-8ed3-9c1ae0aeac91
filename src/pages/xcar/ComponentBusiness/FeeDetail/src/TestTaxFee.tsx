// 测试税费组件的简单示例
import React from 'react';
import TaxFeeItem from './TaxFeeItem';

const TestTaxFee = () => {
  const mockTaxFeeData = {
    title: '税费和其他费用',
    code: 'Tax',
    type: 1,
    currencyCode: 'CNY',
    currentTotalPrice: 100,
    items: [
      {
        title: '机场税',
        description: '',
        code: 'AirportTax',
        type: 1,
        currencyCode: 'CNY',
        currentTotalPrice: 50,
      },
      {
        title: '客户设施费',
        description: '',
        code: 'CustomerFacilityFee',
        type: 1,
        currencyCode: 'CNY',
        currentTotalPrice: 30,
      },
    ],
  };

  return (
    <TaxFeeItem
      title={mockTaxFeeData.title}
      code={mockTaxFeeData.code}
      type={mockTaxFeeData.type}
      currencyCode={mockTaxFeeData.currencyCode}
      currentTotalPrice={mockTaxFeeData.currentTotalPrice}
      items={mockTaxFeeData.items}
    />
  );
};

export default TestTaxFee;
